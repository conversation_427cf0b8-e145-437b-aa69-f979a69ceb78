{"name": "gitator", "version": "1.0.0", "description": "Git branch visualizer built with Electron and TypeScript", "main": "dist/main/main.js", "scripts": {"build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js", "build:renderer": "webpack --config webpack.renderer.config.js", "dev": "npm run build && electron .", "dev:watch": "concurrently \"npm run watch:main\" \"npm run watch:renderer\" \"wait-on dist/main/main.js dist/renderer/app.js && electron .\"", "start": "electron .", "watch": "concurrently \"npm run watch:main\" \"npm run watch:renderer\"", "watch:main": "webpack --config webpack.main.config.js --watch", "watch:renderer": "webpack --config webpack.renderer.config.js --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["git", "electron", "typescript", "branch-visualizer"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "concurrently": "^8.2.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.0", "electron": "^28.0.0", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "ts-loader": "^9.5.0", "typescript": "^5.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.0"}, "dependencies": {"@types/d3": "^7.4.3", "d3": "^7.9.0", "electron-store": "^8.1.0"}}