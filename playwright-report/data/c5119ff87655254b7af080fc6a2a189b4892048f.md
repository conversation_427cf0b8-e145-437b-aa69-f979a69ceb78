# Test info

- Name: Gitator - Tests Completos >> scroll extensivo y selección de commits en diferentes posiciones
- Location: /Users/<USER>/repositorios/gitator/tests/gitator-complete.test.js:548:3

# Error details

```
TimeoutError: locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator('#commit-graph')
    - locator resolved to <canvas width="600" height="12000" id="commit-graph" class="commit-graph pointer"></canvas>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <html lang="es">…</html> intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <html lang="es">…</html> intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <html lang="es">…</html> intercepts pointer events
     - retrying click action
       - waiting 500ms

    at ElectronTestHelper.clickCanvas (/Users/<USER>/repositorios/gitator/tests/setup.js:238:18)
    at /Users/<USER>/repositorios/gitator/tests/gitator-complete.test.js:639:7
```

# Test source

```ts
  138 |   }
  139 |
  140 |   // Helper para seleccionar un repositorio de test
  141 |   async selectTestRepo(page, repoPath = null) {
  142 |     // Configurar mock automático para evitar selección manual repetitiva
  143 |     await page.evaluate(() => {
  144 |       // Mock del electronAPI.selectDirectory para devolver automáticamente el repo de test
  145 |       if (window.electronAPI) {
  146 |         window.electronAPI.selectDirectory = async () => ({
  147 |           path: '/Users/<USER>/repositorios/vscode',
  148 |           name: 'vscode',
  149 |           isGitRepository: true
  150 |         });
  151 |       }
  152 |     });
  153 |
  154 |     // Hacer click en el botón
  155 |     await page.click('#select-repo-btn');
  156 |
  157 |     console.log('🔧 Usando repositorio de test automático: /Users/<USER>/repositorios/vscode');
  158 |
  159 |     // Esperar a que se cargue el repositorio
  160 |     await this.waitForRepoLoad(page);
  161 |   }
  162 |
  163 |   // Helper para obtener información del canvas
  164 |   async getCanvasInfo(page) {
  165 |     return await page.evaluate(() => {
  166 |       const canvas = document.getElementById('commit-graph');
  167 |       const container = document.getElementById('commit-graph-container');
  168 |       const graphContainer = document.getElementById('graph-container');
  169 |
  170 |       if (!canvas || !container) {
  171 |         return {
  172 |           visible: false,
  173 |           canvas: null,
  174 |           container: null
  175 |         };
  176 |       }
  177 |
  178 |       const rect = canvas.getBoundingClientRect();
  179 |       const containerRect = container.getBoundingClientRect();
  180 |       const isVisible = !graphContainer?.classList.contains('hidden') &&
  181 |                        rect.width > 0 && rect.height > 0;
  182 |
  183 |       return {
  184 |         visible: isVisible,
  185 |         width: canvas.width,
  186 |         height: canvas.height,
  187 |         canvas: {
  188 |           width: canvas.width,
  189 |           height: canvas.height,
  190 |           styleWidth: canvas.style.width,
  191 |           styleHeight: canvas.style.height,
  192 |           rect: rect
  193 |         },
  194 |         container: {
  195 |           rect: containerRect,
  196 |           scrollTop: container.scrollTop,
  197 |           scrollHeight: container.scrollHeight
  198 |         }
  199 |       };
  200 |     });
  201 |   }
  202 |
  203 |   // Helper para obtener información de commits
  204 |   async getCommitsInfo(page) {
  205 |     return await page.evaluate(() => {
  206 |       const commitItems = document.querySelectorAll('#commit-list .commit-item');
  207 |       const commitList = document.getElementById('commit-list');
  208 |
  209 |       return {
  210 |         count: commitItems.length,
  211 |         scrollTop: commitList ? commitList.scrollTop : 0,
  212 |         scrollHeight: commitList ? commitList.scrollHeight : 0,
  213 |         commits: Array.from(commitItems).map((item, index) => ({
  214 |           index,
  215 |           sha: item.dataset.sha,
  216 |           subject: item.querySelector('.commit-subject')?.textContent,
  217 |           selected: item.classList.contains('selected'),
  218 |           rect: item.getBoundingClientRect()
  219 |         }))
  220 |       };
  221 |     });
  222 |   }
  223 |
  224 |   // Helper para simular scroll
  225 |   async scrollCommitList(page, scrollTop) {
  226 |     await page.evaluate((scrollTop) => {
  227 |       const commitList = document.getElementById('commit-list');
  228 |       if (commitList) {
  229 |         commitList.scrollTop = scrollTop;
  230 |         commitList.dispatchEvent(new Event('scroll'));
  231 |       }
  232 |     }, scrollTop);
  233 |   }
  234 |
  235 |   // Helper para simular click en canvas
  236 |   async clickCanvas(page, x, y) {
  237 |     const canvas = await page.locator('#commit-graph');
> 238 |     await canvas.click({ position: { x, y } });
      |                  ^ TimeoutError: locator.click: Timeout 30000ms exceeded.
  239 |   }
  240 |
  241 |   // Helper para verificar tooltip
  242 |   async getTooltipInfo(page) {
  243 |     return await page.evaluate(() => {
  244 |       const tooltip = document.getElementById('commit-hash-tooltip');
  245 |       if (!tooltip) return null;
  246 |
  247 |       return {
  248 |         visible: tooltip.classList.contains('visible'),
  249 |         text: tooltip.textContent,
  250 |         style: {
  251 |           left: tooltip.style.left,
  252 |           top: tooltip.style.top
  253 |         }
  254 |       };
  255 |     });
  256 |   }
  257 | }
  258 |
  259 | module.exports = { ElectronTestHelper, test, expect };
  260 |
```