// Servicio Git basado en la lógica de SourceGit Commands/QueryCommits.cs

import { spawn } from 'child_process';
import { GitCommit, GitUser, GitLogResult, QueryCommitsOptions } from '../shared/types/git-types';
import { parseDecorators } from '../shared/models/commit-graph';

export class GitService {
  private static userCache = new Map<string, GitUser>();

  // Método principal para consultar commits (equivalente a QueryCommits en SourceGit)
  static async queryCommits(repoPath: string, options: QueryCommitsOptions = {}): Promise<GitLogResult> {
    try {
      const args = this.buildGitLogArgs(options);
      const result = await this.executeGitCommand(repoPath, args);

      if (!result.success) {
        return { commits: [], success: false, error: result.error };
      }

      const commits = this.parseCommitsOutput(result.stdout);

      // Si needFindHead es true y no se encontró HEAD, marcar el primer merged
      if (options.needFindHead && commits.length > 0 && !commits.some(c => c.isMerged)) {
        await this.markFirstMerged(repoPath, commits);
      }

      return { commits, success: true };
    } catch (error) {
      return {
        commits: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Construir argumentos para git log (equivalente a QueryCommits constructor en SourceGit)
  private static buildGitLogArgs(options: QueryCommitsOptions): string[] {
    const args = ['log', '--no-show-signature', '--decorate=full'];

    // Formato específico de SourceGit: SHA, Parents, Decorators, Author, AuthorTime, Committer, CommitterTime, Subject
    args.push('--format=%H%n%P%n%D%n%aN±%aE%n%at%n%cN±%cE%n%ct%n%s');

    if (options.limits) {
      args.push(`--max-count=${options.limits}`);
    }

    if (options.maxCount) {
      args.push(`-${options.maxCount}`);
    }

    if (options.skip) {
      args.push(`--skip=${options.skip}`);
    }

    if (options.since) {
      args.push(`--since="${options.since}"`);
    }

    if (options.until) {
      args.push(`--until="${options.until}"`);
    }

    if (options.author) {
      args.push(`--author="${options.author}"`);
    }

    if (options.grep) {
      args.push(`--grep="${options.grep}"`);
    }

    if (options.all) {
      args.push('--all');
    }

    return args;
  }

  // Ejecutar comando git
  private static executeGitCommand(repoPath: string, args: string[]): Promise<{ success: boolean; stdout: string; error?: string }> {
    return new Promise((resolve) => {
      const git = spawn('git', args, {
        cwd: repoPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      git.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      git.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      git.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, stdout });
        } else {
          resolve({ success: false, stdout: '', error: stderr || `Git command failed with code ${code}` });
        }
      });

      git.on('error', (error) => {
        resolve({ success: false, stdout: '', error: error.message });
      });
    });
  }

  // Parsear salida de git log (equivalente a QueryCommits.Result() en SourceGit)
  private static parseCommitsOutput(output: string): GitCommit[] {
    const commits: GitCommit[] = [];
    let current: Partial<GitCommit> | null = null;
    let nextPartIdx = 0;
    let isHeadFound = false;

    const lines = output.split('\n');

    for (const line of lines) {
      if (line.trim() === '' && nextPartIdx === 0) continue;

      switch (nextPartIdx) {
        case 0: // SHA
          current = {
            sha: line.trim(),
            parents: [],
            decorators: [],
            isMerged: false,
            hasDecorators: false,
            isCurrentHead: false,
            color: 0,
            opacity: 1,
            margin: { left: 0, top: 0, right: 0, bottom: 0 }
          };
          break;

        case 1: // Parents
          if (current && line.trim().length >= 8) {
            current.parents = line.trim().split(' ').filter(p => p.length > 0);
          }
          break;

        case 2: // Decorators
          if (current) {
            parseDecorators(current as GitCommit, line);
            if (current.isMerged && !isHeadFound) {
              isHeadFound = true;
            }
          }
          break;

        case 3: // Author
          if (current) {
            current.author = this.findOrAddUser(line);
          }
          break;

        case 4: // Author Time
          if (current) {
            current.authorTime = parseInt(line);
            current.authorTimeStr = this.formatDateTime(current.authorTime);
            current.authorTimeShortStr = this.formatDateOnly(current.authorTime);
          }
          break;

        case 5: // Committer
          if (current) {
            current.committer = this.findOrAddUser(line);
          }
          break;

        case 6: // Committer Time
          if (current) {
            current.committerTime = parseInt(line);
            current.committerTimeStr = this.formatDateTime(current.committerTime);
            current.committerTimeShortStr = this.formatDateOnly(current.committerTime);
            current.isCommitterVisible = !this.usersEqual(current.author!, current.committer!) ||
                                       current.authorTime !== current.committerTime;
          }
          break;

        case 7: // Subject
          if (current) {
            current.subject = line;
            current.opacity = current.isMerged ? 1 : 0.65; // OpacityForNotMerged
            commits.push(current as GitCommit);
            current = null;
            nextPartIdx = -1;
          }
          break;
      }

      nextPartIdx++;
    }

    return commits;
  }

  // Encontrar o agregar usuario (equivalente a User.FindOrAdd en SourceGit)
  private static findOrAddUser(line: string): GitUser {
    const parts = line.split('±');
    if (parts.length !== 2) {
      return { name: line, email: '' };
    }

    const key = line;
    if (this.userCache.has(key)) {
      return this.userCache.get(key)!;
    }

    const user: GitUser = {
      name: parts[0],
      email: parts[1]
    };

    this.userCache.set(key, user);
    return user;
  }

  // Comparar usuarios
  private static usersEqual(user1: GitUser, user2: GitUser): boolean {
    return user1.name === user2.name && user1.email === user2.email;
  }

  // Formatear fecha y hora
  private static formatDateTime(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString();
  }

  // Formatear solo fecha
  private static formatDateOnly(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleDateString();
  }

  // Marcar primer merged (equivalente a MarkFirstMerged en SourceGit)
  private static async markFirstMerged(repoPath: string, commits: GitCommit[]): Promise<void> {
    if (commits.length === 0) return;

    const lastCommit = commits[commits.length - 1];
    const args = ['log', `--since="${lastCommit.committerTimeStr}"`, '--format=%H'];

    const result = await this.executeGitCommand(repoPath, args);
    if (!result.success) return;

    const shas = new Set(result.stdout.split('\n').filter(sha => sha.trim().length > 0));

    for (const commit of commits) {
      if (shas.has(commit.sha)) {
        commit.isMerged = true;
        break;
      }
    }
  }

  // Verificar si un directorio es un repositorio Git
  static async isGitRepository(path: string): Promise<boolean> {
    try {
      const result = await this.executeGitCommand(path, ['rev-parse', '--git-dir']);
      return result.success;
    } catch {
      return false;
    }
  }

  // Obtener nombre del repositorio
  static async getRepositoryName(path: string): Promise<string> {
    try {
      const result = await this.executeGitCommand(path, ['rev-parse', '--show-toplevel']);
      if (result.success) {
        const parts = result.stdout.trim().split('/');
        return parts[parts.length - 1] || 'Unknown';
      }
    } catch {
      // Fallback al nombre del directorio
    }

    const parts = path.split('/');
    return parts[parts.length - 1] || 'Unknown';
  }
}
