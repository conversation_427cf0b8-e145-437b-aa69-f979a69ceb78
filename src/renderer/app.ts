// Aplicación principal del renderer - Gitator con D3.js

import { GitCommit, GitLogResult, QueryCommitsOptions, DirectoryInfo, DecoratorType } from '../shared/types/git-types';
import { CommitGraphGenerator } from '../shared/models/commit-graph';
import * as d3 from 'd3';

// Declarar la API de Electron
declare global {
  interface Window {
    electronAPI: {
      selectDirectory: () => Promise<DirectoryInfo | null>;
      queryCommits: (repoPath: string, options: QueryCommitsOptions) => Promise<GitLogResult>;
      isGitRepository: (path: string) => Promise<boolean>;
      getRepositoryName: (path: string) => Promise<string>;
      quitApp: () => Promise<void>;
      minimizeWindow: () => Promise<void>;
      toggleMaximizeWindow: () => Promise<void>;
    };
  }
}

// Interfaz para posiciones de dots de commits
interface CommitDotPosition {
  commit: GitCommit;
  hash: string;
  x: number;
  y: number;
  radius: number;
  index: number;
}

class GitatorApp {
  private currentRepo: DirectoryInfo | null = null;
  public commits: GitCommit[] = []; // Public para tests
  private graphGenerator: CommitGraphGenerator;
  private svg: d3.Selection<SVGSVGElement, unknown, HTMLElement, any> | null = null;
  private svgContainer: d3.Selection<SVGGElement, unknown, HTMLElement, any> | null = null;
  public commitDots: CommitDotPosition[] = []; // Public para tests
  private graphContainer: HTMLElement | null = null;
  private selectedCommitHash: string | null = null;
  private hashTooltip: HTMLElement | null = null;
  // private zoom: d3.ZoomBehavior<SVGSVGElement, unknown> | null = null;

  // Propiedades para lazy loading
  private readonly PAGE_SIZE = 100;
  private currentOffset = 0;
  private isLoadingMore = false;
  private hasMoreCommits = true;
  private isInitialLoad = true;

  constructor() {
    this.graphGenerator = new CommitGraphGenerator();
    this.initializeApp();
  }

  private initializeApp(): void {
    this.setupEventListeners();
    this.initializeSVG();
    this.updateUI();
  }

  private setupEventListeners(): void {
    // Controles de ventana
    document.getElementById('minimize-btn')?.addEventListener('click', () => {
      window.electronAPI.minimizeWindow();
    });

    document.getElementById('maximize-btn')?.addEventListener('click', () => {
      window.electronAPI.toggleMaximizeWindow();
    });

    document.getElementById('close-btn')?.addEventListener('click', () => {
      window.electronAPI.quitApp();
    });

    // Selección de repositorio
    document.getElementById('select-repo-btn')?.addEventListener('click', () => {
      this.selectRepository();
    });

    // Controles del grafo
    document.getElementById('refresh-btn')?.addEventListener('click', () => {
      this.refreshCommits();
    });

    document.getElementById('retry-btn')?.addEventListener('click', () => {
      this.refreshCommits();
    });

    // Opciones del grafo
    document.getElementById('show-all-branches')?.addEventListener('change', () => {
      this.refreshCommits();
    });

    document.getElementById('first-parent-only')?.addEventListener('change', () => {
      this.refreshCommits();
    });
  }

  private initializeSVG(): void {
    this.graphContainer = document.getElementById('commit-graph-container');
    this.hashTooltip = document.getElementById('commit-hash-tooltip');

    // Configurar scroll listener para lazy loading
    this.setupScrollListener();

    // Crear SVG con D3
    this.svg = d3.select('#commit-graph') as d3.Selection<SVGSVGElement, unknown, HTMLElement, any>;
    this.svg
      .attr('width', '100%')
      .attr('height', '100%');

    // Crear contenedor principal con zoom y pan
    this.svgContainer = this.svg.append('g')
      .attr('class', 'main-container');

    // Configurar zoom y pan
    // this.setupZoomAndPan();
    this.setupSVGEvents();
    this.setupTooltipEvents();
  }

  private setupScrollListener(): void {
    if (!this.graphContainer) return;

    this.graphContainer.addEventListener('scroll', () => {
      if (this.isLoadingMore || !this.hasMoreCommits) return;

      const scrollTop = this.graphContainer!.scrollTop;
      const scrollHeight = this.graphContainer!.scrollHeight;
      const clientHeight = this.graphContainer!.clientHeight;

      // Cargar más cuando esté cerca del final (200px antes del final)
      const threshold = 200;
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

      if (distanceFromBottom <= threshold) {
        // console.log(`[Scroll] Trigger lazy load - Distance from bottom: ${distanceFromBottom}px, Commits: ${this.commits.length}`);
        this.loadMoreCommits();
      }
    });
  }

  // private setupZoomAndPan(): void {
  //   if (!this.svg || !this.svgContainer) return;
  //
  //   this.zoom = d3.zoom<SVGSVGElement, unknown>()
  //     .scaleExtent([0.1, 3])
  //     .on('zoom', (event) => {
  //       this.svgContainer!.attr('transform', event.transform);
  //     });
  //
  //   this.svg.call(this.zoom);
  // }

  private setupSVGEvents(): void {
    if (!this.svg) return;

    // Configurar redimensionamiento
    const resizeSVG = () => {
      if (!this.svg || !this.graphContainer) return;

      const rect = this.graphContainer.getBoundingClientRect();
      this.svg
        .attr('width', rect.width)
        .attr('height', rect.height);

      // Redibujar si hay commits
      if (this.commits.length > 0) {
        this.drawCommitGraph();
      }
    };

    window.addEventListener('resize', resizeSVG);
    resizeSVG();
  }

  private setupTooltipEvents(): void {
    if (!this.graphContainer) return;

    // Ocultar tooltip al hacer scroll
    this.graphContainer.addEventListener('scroll', () => {
      this.hideHashTooltip();
    });
  }

  private async selectRepository(): Promise<void> {
    try {
      const directory = await window.electronAPI.selectDirectory();
      if (!directory) return;

      this.currentRepo = directory;
      this.updateRepositoryInfo();

      if (directory.isGitRepository) {
        await this.loadCommits();
      } else {
        this.showError('El directorio seleccionado no es un repositorio Git válido');
      }
    } catch (error) {
      this.showError(`Error al seleccionar repositorio: ${error}`);
    }
  }

  private updateRepositoryInfo(): void {
    const repoNameEl = document.getElementById('repo-name');
    const repoPathEl = document.getElementById('repo-path');

    if (repoNameEl && this.currentRepo) {
      repoNameEl.textContent = this.currentRepo.name;
    }

    if (repoPathEl && this.currentRepo) {
      repoPathEl.textContent = this.currentRepo.path;
    }
  }

  private getQueryOptions(): QueryCommitsOptions {
    const showAllBranches = (document.getElementById('show-all-branches') as HTMLInputElement)?.checked ?? false;

    return {
      limits: this.PAGE_SIZE.toString(),
      skip: this.currentOffset,
      all: showAllBranches
    };
  }

  private async loadCommits(): Promise<void> {
    if (!this.currentRepo?.isGitRepository) return;

    this.hideHashTooltip(); // Ocultar tooltip al cargar nuevos commits

    // Reset para carga inicial
    if (this.isInitialLoad) {
      this.commits = [];
      this.currentOffset = 0;
      this.hasMoreCommits = true;
      this.showLoading();
    }

    try {
      const options = this.getQueryOptions();
      const result = await window.electronAPI.queryCommits(this.currentRepo.path, options);

      if (result.success) {
        // Log de la carga
        if (result.commits.length > 0) {
          const loadType = this.isInitialLoad ? 'Carga Inicial' : 'Lazy Load';
          console.log(`[${loadType}] Cargando ${result.commits.length} commits desde: ${result.commits[0].sha} (${result.commits[0].subject})`);
        }

        if (this.isInitialLoad) {
          this.commits = result.commits;
        } else {
          this.commits = [...this.commits, ...result.commits];
        }

        // Verificar si hay más commits
        this.hasMoreCommits = result.commits.length === this.PAGE_SIZE;
        this.currentOffset += result.commits.length;

        this.generateAndDrawGraph();
        this.updateCommitCount();

        if (this.isInitialLoad) {
          this.showGraph();
          this.isInitialLoad = false;
        }

        this.setStatus(`Cargados ${this.commits.length} commits`);
      } else {
        this.showError(result.error || 'Error al cargar commits');
      }
    } catch (error) {
      this.showError(`Error al cargar commits: ${error}`);
    } finally {
      if (this.isInitialLoad) {
        this.hideLoading();
      }
    }
  }

  private async loadMoreCommits(): Promise<void> {
    if (this.isLoadingMore || !this.hasMoreCommits || !this.currentRepo?.isGitRepository) return;

    this.isLoadingMore = true;
    this.showLoadingMore();

    try {
      const options = this.getQueryOptions();
      const result = await window.electronAPI.queryCommits(this.currentRepo.path, options);

      if (result.success) {
        // Log del lazy loading
        if (result.commits.length > 0) {
          console.log(`[Lazy Load] Cargando ${result.commits.length} commits desde: ${result.commits[0].sha} (${result.commits[0].subject})`);
        }

        const previousCommitCount = this.commits.length;

        // Añadir nuevos commits al final
        this.commits = [...this.commits, ...result.commits];

        // Verificar si hay más commits
        this.hasMoreCommits = result.commits.length === this.PAGE_SIZE;
        this.currentOffset += result.commits.length;

        // console.log(`[Lazy Load] Total commits antes: ${previousCommitCount}, después: ${this.commits.length}`);

        // Regenerar el grafo completo
        this.generateAndDrawGraph();
        this.updateCommitCount();

        // Forzar actualización del contenedor de scroll
        this.forceScrollContainerUpdate();

        this.setStatus(`Cargados ${this.commits.length} commits`);
      } else {
        this.setStatus('Error al cargar más commits');
      }
    } catch (error) {
      console.error('[Lazy Load] Error:', error);
      this.setStatus(`Error al cargar más commits: ${error}`);
    } finally {
      this.isLoadingMore = false;
      this.hideLoadingMore();
    }
  }

  private generateAndDrawGraph(): void {
    if (this.commits.length === 0) return;

    // console.log(`[Graph] Generando grafo para ${this.commits.length} commits`);

    const firstParentOnly = (document.getElementById('first-parent-only') as HTMLInputElement)?.checked ?? false;
    const graph = this.graphGenerator.parse(this.commits, firstParentOnly);

    // console.log(`[Graph] Grafo generado - Paths: ${graph.paths.length}, Links: ${graph.links.length}, Dots: ${graph.dots.length}`);

    this.drawCommitGraph();
  }

  private forceScrollContainerUpdate(): void {
    if (!this.graphContainer) return;

    // Forzar recalculo del scroll height
    const currentScrollTop = this.graphContainer.scrollTop;
    this.graphContainer.style.height = 'auto';

    // Usar requestAnimationFrame para asegurar que el DOM se actualice
    requestAnimationFrame(() => {
      if (this.graphContainer) {
        this.graphContainer.style.height = '';
        this.graphContainer.scrollTop = currentScrollTop;
        console.log(`[Scroll] Container actualizado - ScrollHeight: ${this.graphContainer.scrollHeight}px`);
      }
    });
  }

  private drawCommitGraph(): void {
    if (!this.svg || !this.svgContainer || this.commits.length === 0) return;

    console.log(`[Draw] Dibujando grafo para ${this.commits.length} commits`);

    // Limpiar contenido anterior
    this.svgContainer.selectAll('*').remove();
    this.commitDots = [];

    const commitRowHeight = 30;
    const totalHeight = this.commits.length * commitRowHeight;
    const containerRect = this.graphContainer?.getBoundingClientRect();
    const svgWidth = containerRect?.width || 800;

    // console.log(`[Draw] SVG dimensions - Width: ${svgWidth}px, Height: ${totalHeight}px`);

    // Configurar el tamaño del SVG
    this.svg
      .attr('width', svgWidth)
      .attr('height', totalHeight);

    const firstParentOnly = (document.getElementById('first-parent-only') as HTMLInputElement)?.checked ?? false;
    const graph = this.graphGenerator.parse(this.commits, firstParentOnly);
    const colors = this.graphGenerator.getColors();


    console.log(graph)

    // Dibujar paths (líneas de conexión)
    this.drawPaths(graph.paths, colors);

    // Dibujar links (conexiones de merge)
    this.drawLinks(graph.links, colors);

    // Dibujar commits (dots + información)
    this.drawCommits(graph.dots, colors, svgWidth);

    // console.log(`[Draw] Grafo dibujado - Dots creados: ${this.commitDots.length}`);
  }

  private selectCommit(sha: string): void {
    // Actualizar la selección
    this.selectedCommitHash = sha;

    // Redibujar el grafo para mostrar la selección
    this.drawCommitGraph();

    // Hacer scroll automático para que el commit sea visible
    this.scrollToCommit(sha);
  }

  private scrollToCommit(sha: string): void {
    if (!this.graphContainer) return;

    // Encontrar el índice del commit
    const commitIndex = this.commits.findIndex(c => c.sha === sha);
    if (commitIndex === -1) return;

    const commitRowHeight = 30;
    const commitY = commitIndex * commitRowHeight;
    const containerHeight = this.graphContainer.clientHeight;

    // Calcular si el commit está visible
    const scrollTop = this.graphContainer.scrollTop;
    const isVisible = (
      commitY >= scrollTop &&
      commitY + commitRowHeight <= scrollTop + containerHeight
    );

    if (!isVisible) {
      // Centrar el commit en el contenedor
      const targetScrollTop = commitY - (containerHeight / 2) + (commitRowHeight / 2);

      // Hacer scroll suave
      this.graphContainer.scrollTo({
        top: Math.max(0, targetScrollTop),
        behavior: 'smooth'
      });
    }
  }

  private showHashTooltip(commit: GitCommit, x: number, y: number): void {
    if (!this.hashTooltip || !this.graphContainer) return;

    // Mostrar solo los primeros 6 caracteres del hash
    this.hashTooltip.textContent = commit.sha.substring(0, 6);

    // Posicionar el tooltip
    const containerRect = this.graphContainer.getBoundingClientRect();
    const tooltipWidth = 80; // Ancho aproximado del tooltip (6 chars + padding)
    const tooltipHeight = 40; // Altura aproximada del tooltip

    // Calcular posición X (centrado en el punto, pero sin salirse del contenedor)
    let tooltipX = x - (tooltipWidth / 2);
    tooltipX = Math.max(10, Math.min(tooltipX, containerRect.width - tooltipWidth - 10));

    // Calcular posición Y (arriba del punto)
    let tooltipY = y - tooltipHeight - 10;
    if (tooltipY < 10) {
      tooltipY = y + 20; // Si no cabe arriba, ponerlo abajo
    }

    this.hashTooltip.style.left = `${tooltipX}px`;
    this.hashTooltip.style.top = `${tooltipY}px`;

    // Mostrar el tooltip
    this.hashTooltip.classList.add('visible');
  }

  private hideHashTooltip(): void {
    if (!this.hashTooltip) return;
    this.hashTooltip.classList.remove('visible');
  }

  private updateCommitCount(): void {
    const countEl = document.getElementById('commit-count');
    if (countEl) {
      countEl.textContent = `${this.commits.length} commits`;
    }
  }

  private async refreshCommits(): Promise<void> {
    if (this.currentRepo?.isGitRepository) {
      // Reset del estado para una nueva carga
      this.isInitialLoad = true;
      this.currentOffset = 0;
      this.hasMoreCommits = true;
      this.isLoadingMore = false;
      await this.loadCommits();
    }
  }

  private showLoading(): void {
    this.hideAllViews();
    document.getElementById('loading')?.classList.remove('hidden');
    this.setStatus('Cargando commits...');
  }

  private showGraph(): void {
    this.hideAllViews();
    document.getElementById('graph-container')?.classList.remove('hidden');
  }

  private showLoadingMore(): void {
    document.getElementById('loading-more')?.classList.remove('hidden');
  }

  private hideLoadingMore(): void {
    document.getElementById('loading-more')?.classList.add('hidden');
  }

  private showError(message: string): void {
    this.hideAllViews();
    const errorEl = document.getElementById('error');
    const errorMessageEl = document.getElementById('error-message');

    if (errorEl && errorMessageEl) {
      errorMessageEl.textContent = message;
      errorEl.classList.remove('hidden');
    }

    this.setStatus(`Error: ${message}`);
  }

  private hideLoading(): void {
    document.getElementById('loading')?.classList.add('hidden');
  }

  private hideAllViews(): void {
    document.getElementById('no-repo')?.classList.add('hidden');
    document.getElementById('loading')?.classList.add('hidden');
    document.getElementById('error')?.classList.add('hidden');
    document.getElementById('graph-container')?.classList.add('hidden');
  }

  private updateUI(): void {
    if (this.currentRepo?.isGitRepository && this.commits.length > 0) {
      this.showGraph();
    } else if (this.currentRepo) {
      this.showError('No es un repositorio Git válido');
    } else {
      document.getElementById('no-repo')?.classList.remove('hidden');
    }
  }

  private setStatus(message: string): void {
    const statusEl = document.getElementById('status-text');
    if (statusEl) {
      statusEl.textContent = message;
    }
  }

  // ==================== MÉTODOS D3.JS ====================

  private drawPaths(paths: any[], colors: string[]): void {
    if (!this.svgContainer) return;

    const pathsGroup = this.svgContainer.append('g').attr('class', 'paths');
    const commitRowHeight = 30;

    paths.forEach(path => {
      if (path.points.length < 2) return;

      const line = d3.line<any>()
        .x(d => d.x)
        .y(d => (d.y * commitRowHeight) + (commitRowHeight / 2));

      pathsGroup.append('path')
        .datum(path.points)
        .attr('class', 'branch-line')
        .attr('d', line)
        .attr('stroke', colors[path.color % colors.length])
        .attr('stroke-width', 2)
        .attr('opacity', path.isMerged ? 1 : this.graphGenerator.getOpacityForNotMerged());
    });
  }

  private drawLinks(links: any[], colors: string[]): void {
    if (!this.svgContainer) return;

    const linksGroup = this.svgContainer.append('g').attr('class', 'links');
    const commitRowHeight = 30;

    links.forEach(link => {
      const startY = (link.start.y * commitRowHeight) + (commitRowHeight / 2);
      const endY = (link.end.y * commitRowHeight) + (commitRowHeight / 2);
      const controlY = (link.control.y * commitRowHeight) + (commitRowHeight / 2);

      // Crear path para curva cuadrática
      const pathData = `M ${link.start.x} ${startY} Q ${link.control.x} ${controlY} ${link.end.x} ${endY}`;

      linksGroup.append('path')
        .attr('class', 'merge-line')
        .attr('d', pathData)
        .attr('stroke', colors[link.color % colors.length])
        .attr('stroke-width', 2)
        .attr('fill', 'none')
        .attr('opacity', link.isMerged ? 1 : this.graphGenerator.getOpacityForNotMerged());
    });
  }

  private drawCommits(dots: any[], colors: string[], svgWidth: number): void {
    if (!this.svgContainer) return;

    const commitsGroup = this.svgContainer.append('g').attr('class', 'commits');
    const commitRowHeight = 30;

    dots.forEach((dot, index) => {
      if (index >= this.commits.length) return;

      const commit = this.commits[index];
      const y = (index * commitRowHeight) + (commitRowHeight / 2);
      const x = dot.center.x;
      const radius = dot.type === 1 ? 5 : 4;
      const isSelected = this.selectedCommitHash === commit.sha;

      // Crear grupo para cada commit
      const commitGroup = commitsGroup.append('g')
        .attr('class', `commit-row ${isSelected ? 'selected' : ''}`)
        .attr('data-sha', commit.sha);

      // Fondo de selección
      if (isSelected) {
        commitGroup.append('rect')
          .attr('class', 'commit-background')
          .attr('x', 120)
          .attr('y', index * commitRowHeight)
          .attr('width', svgWidth - 120)
          .attr('height', commitRowHeight)
          .attr('fill', '#094771');
      }

      // Dot del commit
      const dotElement = commitGroup.append('circle')
        .attr('class', 'commit-dot')
        .attr('cx', x)
        .attr('cy', y)
        .attr('r', radius)
        .attr('fill', colors[dot.color % colors.length])
        .attr('stroke', '#1e1e1e')
        .attr('stroke-width', 1)
        .style('cursor', 'pointer');

      if (isSelected) {
        dotElement
          .attr('stroke', '#ffffff')
          .attr('stroke-width', 2);
      }

      // Almacenar posición del dot para tests
      this.commitDots.push({
        commit: commit,
        hash: commit.sha,
        x,
        y,
        radius: radius + 2,
        index
      });

      // Información del commit
      this.drawCommitInfo(commitGroup, commit, y, svgWidth, isSelected);

      // Eventos de click
      commitGroup
        .style('cursor', 'pointer')
        .on('click', () => {
          this.selectCommit(commit.sha);
          this.showHashTooltip(commit, x, y);
        })
        .on('mouseenter', () => {
          if (!isSelected) {
            commitGroup.select('.commit-background')
              .remove();
            commitGroup.insert('rect', ':first-child')
              .attr('class', 'commit-background')
              .attr('x', 120)
              .attr('y', index * commitRowHeight)
              .attr('width', svgWidth - 120)
              .attr('height', commitRowHeight)
              .attr('fill', '#2d2d30')
              .attr('opacity', 0.5);
          }
        })
        .on('mouseleave', () => {
          if (!isSelected) {
            commitGroup.select('.commit-background').remove();
          }
        });
    });
  }

  private drawCommitInfo(group: d3.Selection<SVGGElement, unknown, HTMLElement, any>, commit: GitCommit, y: number, svgWidth: number, isSelected: boolean): void {
    let currentX = 130; // Espacio para el grafo
    const spacing = 15;

    // Hash del commit
    const hashText = group.append('text')
      .attr('class', 'commit-hash')
      .attr('x', currentX)
      .attr('y', y)
      .attr('dy', '0.35em')
      .attr('font-family', '"Courier New", monospace')
      .attr('font-size', '12px')
      .attr('fill', '#4ec9b0')
      .text(commit.sha.substring(0, 8));

    currentX += hashText.node()!.getBBox().width + spacing;

    // Fecha
    const dateText = group.append('text')
      .attr('class', 'commit-date')
      .attr('x', currentX)
      .attr('y', y)
      .attr('dy', '0.35em')
      .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
      .attr('font-size', '12px')
      .attr('fill', '#888888')
      .text(commit.authorTimeShortStr);

    currentX += dateText.node()!.getBBox().width + spacing;

    // Autor
    const authorText = group.append('text')
      .attr('class', 'commit-author')
      .attr('x', currentX)
      .attr('y', y)
      .attr('dy', '0.35em')
      .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
      .attr('font-size', '12px')
      .attr('fill', '#569cd6')
      .text(commit.author.name);

    currentX += authorText.node()!.getBBox().width + spacing;

    // Título del commit (truncado si es necesario)
    const remainingWidth = svgWidth - currentX - 20;
    let subjectText = commit.subject;

    // Crear texto temporal para medir
    const tempText = group.append('text')
      .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
      .attr('font-size', '12px')
      .text(subjectText)
      .style('visibility', 'hidden');

    // Truncar si es necesario
    if (tempText.node()!.getBBox().width > remainingWidth) {
      while (tempText.node()!.getBBox().width > remainingWidth && subjectText.length > 0) {
        subjectText = subjectText.slice(0, -1);
        tempText.text(subjectText + '...');
      }
      subjectText += '...';
    }

    tempText.remove();

    // Título final
    group.append('text')
      .attr('class', 'commit-subject')
      .attr('x', currentX)
      .attr('y', y)
      .attr('dy', '0.35em')
      .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
      .attr('font-size', '12px')
      .attr('fill', isSelected ? '#ffffff' : '#d4d4d4')
      .text(subjectText);

    // Decoradores
    if (commit.hasDecorators) {
      currentX += 200; // Espacio aproximado para el título
      this.drawDecoratorsD3(group, commit.decorators, currentX, y, svgWidth);
    }
  }

  private drawDecoratorsD3(group: d3.Selection<SVGGElement, unknown, HTMLElement, any>, decorators: any[], startX: number, y: number, svgWidth: number): void {
    let currentX = startX;
    const decoratorHeight = 16;
    const decoratorPadding = 4;
    const decoratorSpacing = 6;

    decorators.forEach(decorator => {
      if (currentX >= svgWidth - 50) return;

      // Configurar color según el tipo
      let bgColor = '#0e639c';
      let textColor = '#ffffff';

      switch (decorator.type) {
        case 0: // CurrentBranchHead
        case 1: // CurrentCommitHead
          bgColor = '#43aa8b';
          break;
        case 2: // LocalBranchHead
          bgColor = '#0e639c';
          break;
        case 3: // RemoteBranchHead
          bgColor = '#f8961e';
          break;
        case 4: // Tag
          bgColor = '#f9c74f';
          textColor = '#1e1e1e';
          break;
      }

      // Crear texto temporal para medir
      const tempText = group.append('text')
        .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
        .attr('font-size', '10px')
        .text(decorator.name)
        .style('visibility', 'hidden');

      const textWidth = tempText.node()!.getBBox().width;
      tempText.remove();

      const decoratorWidth = textWidth + (decoratorPadding * 2);

      // Fondo del decorador
      group.append('rect')
        .attr('x', currentX)
        .attr('y', y - decoratorHeight / 2)
        .attr('width', decoratorWidth)
        .attr('height', decoratorHeight)
        .attr('fill', bgColor)
        .attr('rx', 3);

      // Texto del decorador
      group.append('text')
        .attr('x', currentX + decoratorPadding)
        .attr('y', y)
        .attr('dy', '0.35em')
        .attr('font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif')
        .attr('font-size', '10px')
        .attr('fill', textColor)
        .text(decorator.name);

      currentX += decoratorWidth + decoratorSpacing;
    });
  }
}

// Inicializar la aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
  const app = new GitatorApp();
  // Exponer la instancia para tests
  (window as any).gitatorApp = app;
});
