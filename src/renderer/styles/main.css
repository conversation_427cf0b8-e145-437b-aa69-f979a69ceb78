/* Estilos principales para Gitator */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1e1e1e;
    color: #d4d4d4;
    overflow: hidden;
    user-select: none;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    -webkit-app-region: drag;
}

.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    height: 40px;
}

.title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.title h1 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.repo-name {
    font-size: 14px;
    color: #cccccc;
    opacity: 0.8;
}

.window-controls {
    display: flex;
    gap: 8px;
    -webkit-app-region: no-drag;
}

.window-control-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: #cccccc;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.window-control-btn:hover {
    background: #404040;
}

.window-control-btn.close:hover {
    background: #e74c3c;
    color: white;
}

/* Main content */
.app-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #252526;
    border-right: 1px solid #3e3e42;
    padding: 16px;
    overflow-y: auto;
}

.repo-selector {
    margin-bottom: 24px;
}

.select-repo-button {
    width: 100%;
    padding: 12px 16px;
    background: #0e639c;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.select-repo-button:hover {
    background: #1177bb;
}

.select-repo-button:active {
    background: #0d5a8a;
}

.repo-info {
    margin-top: 12px;
    padding: 12px;
    background: #2d2d30;
    border-radius: 6px;
    border: 1px solid #3e3e42;
}

.repo-path {
    font-size: 12px;
    color: #cccccc;
    word-break: break-all;
    margin-bottom: 8px;
}

.repo-status {
    font-size: 12px;
    font-weight: 500;
}

.repo-status.valid {
    color: #4ec9b0;
}

.repo-status.invalid {
    color: #f44747;
}

.graph-controls h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #ffffff;
}

.control-group {
    margin-bottom: 12px;
}

.control-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    cursor: pointer;
}

.control-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.control-group input[type="number"] {
    width: 80px;
    padding: 4px 8px;
    background: #3c3c3c;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    color: #d4d4d4;
    font-size: 13px;
}

.refresh-button {
    width: 100%;
    padding: 8px 12px;
    background: #2d2d30;
    color: #cccccc;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.refresh-button:hover:not(:disabled) {
    background: #404040;
    border-color: #6a6a6a;
}

.refresh-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Graph area */
.graph-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.loading, .no-repo, .error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1e1e1e;
}

.loading .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #3e3e42;
    border-top: 3px solid #0e639c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-repo-content, .error-content {
    text-align: center;
    max-width: 400px;
}

.no-repo-content h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #ffffff;
}

.no-repo-content p {
    font-size: 16px;
    margin-bottom: 24px;
    color: #cccccc;
    line-height: 1.5;
}

.error-content h3 {
    font-size: 20px;
    margin-bottom: 12px;
    color: #f44747;
}

.error-content p {
    font-size: 14px;
    margin-bottom: 16px;
    color: #cccccc;
}

.retry-button {
    padding: 8px 16px;
    background: #f44747;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-button:hover {
    background: #d73a49;
}

.graph-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.graph-header {
    padding: 12px 16px;
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.graph-info {
    font-size: 13px;
    color: #cccccc;
}

.graph-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.commit-graph-container {
    flex: 1;
    background: #1e1e1e;
    overflow-y: auto;
    position: relative;
}

.commit-hash-tooltip {
    position: absolute;
    background: #2d2d30;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #4ec9b0;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    white-space: nowrap;
    opacity: 0;
    transform: translateY(-100%);
    transition: opacity 0.2s ease;
}

.commit-hash-tooltip.visible {
    opacity: 1;
}

.commit-hash-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #2d2d30;
}

.commit-graph {
    width: 100%;
    height: 100%;
    background: #1e1e1e;
    display: block;
    cursor: default;
}

.commit-graph.pointer {
    cursor: pointer;
}

/* Estilos para elementos SVG */
.commit-dot {
    cursor: pointer;
    transition: r 0.2s ease;
}

.commit-dot:hover {
    r: 7;
}

.commit-dot.selected {
    stroke: #ffffff;
    stroke-width: 2;
}

.commit-text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 12px;
    cursor: pointer;
    user-select: none;
}

.commit-hash {
    font-family: 'Courier New', monospace;
    fill: #4ec9b0;
}

.commit-date {
    fill: #888888;
}

.commit-author {
    fill: #569cd6;
}

.commit-subject {
    fill: #d4d4d4;
}

.commit-row.selected .commit-subject {
    fill: #ffffff;
}

.commit-row.selected .commit-background {
    fill: #094771;
}

.branch-line {
    stroke-width: 2;
    fill: none;
}

.merge-line {
    stroke-width: 2;
    fill: none;
    stroke-dasharray: 3,3;
}

.decorator {
    font-size: 10px;
    font-weight: 500;
}

.decorator.branch {
    fill: #0e639c;
}

.decorator.tag {
    fill: #f9c74f;
}

.decorator.remote {
    fill: #f8961e;
}

.decorator.head {
    fill: #43aa8b;
}

/* Status bar */
.status-bar {
    height: 24px;
    background: #007acc;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    font-size: 12px;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #2d2d30;
}

::-webkit-scrollbar-thumb {
    background: #5a5a5a;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6a6a6a;
}

/* Loading more indicator */
.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    background: #252526;
    border-top: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 14px;
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #3e3e42;
    border-top: 2px solid #0e639c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
